# 🌲 Evergreen Projects Portfolio

> **Professional Software Development Portfolio**  
> Showcasing expertise in modern application development across mobile, desktop, and backend technologies

[![Portfolio](https://img.shields.io/badge/Portfolio-Professional-blue)](https://github.com/amabd34/evergreen-projects-portfolio)
[![Technologies](https://img.shields.io/badge/Technologies-13+-green)](#-technology-stack)
[![Projects](https://img.shields.io/badge/Projects-13-orange)](#-featured-projects)

## 👨‍💻 **About This Portfolio**

This repository demonstrates my experience in developing **enterprise-grade applications** across multiple platforms and technologies. Each project showcases different aspects of modern software development, from mobile applications to backend services, following industry best practices and security standards.

**🔒 Security Note**: This is a portfolio version containing project structures, documentation, and configuration templates. All proprietary source code has been removed while preserving the architectural and technical demonstrations.

## 🚀 **Featured Projects**

### **📱 Mobile Applications**

#### **FiberAl React Native** - Social Media Platform
- **Technology**: React Native, Expo, Firebase, TypeScript
- **Features**: Real-time messaging, media sharing, social networking
- **Architecture**: Redux state management, Firebase backend integration
- **Highlights**: Cross-platform mobile development, real-time features

#### **Backsight Management Suite** (7 Applications)
- **Technology**: Expo, React Native, TypeScript, Redux Toolkit
- **Applications**: Inventory, Logistics, Production, Property, Tasks, Manager, Pro
- **Features**: Enterprise management solutions, multi-tenant architecture
- **Highlights**: Scalable mobile enterprise applications

#### **Order Management App** - Customer Interface
- **Technology**: Expo, React Native, TypeScript
- **Features**: Order processing, customer management, real-time updates
- **Architecture**: Modern mobile app with backend integration

### **🖥️ Desktop Applications**

#### **FiberAl Desktop** - Social Media Management
- **Technology**: Angular, Electron, Node.js, TypeScript
- **Features**: Desktop social media management, real-time communication
- **Architecture**: Angular frontend with Node.js backend integration
- **Highlights**: Cross-platform desktop development

### **⚙️ Backend Services**

#### **Order App Backend** - RESTful API Service
- **Technology**: Node.js, TypeScript, Express, MongoDB
- **Features**: RESTful API, authentication, database management
- **Architecture**: Microservices-ready backend with MongoDB integration
- **Highlights**: Modern backend development practices

## 🛠️ **Technology Stack**

### **Frontend Technologies**
- **Mobile**: React Native, Expo SDK
- **Desktop**: Angular, Electron
- **State Management**: Redux, Redux Toolkit
- **UI/UX**: React Navigation, Angular Material
- **Languages**: TypeScript, JavaScript

### **Backend Technologies**
- **Runtime**: Node.js
- **Frameworks**: Express.js
- **Databases**: MongoDB with Mongoose ODM
- **Authentication**: JWT, bcrypt
- **Real-time**: Socket.io

### **Development Tools & Practices**
- **Code Quality**: ESLint, Prettier, TypeScript
- **Testing**: Jest, Expo Testing Library
- **Build Tools**: Metro, Angular CLI, Webpack
- **Version Control**: Git with conventional commits
- **Security**: Environment variables, secure authentication

### **Cloud & DevOps**
- **Cloud Services**: Firebase (Authentication, Storage, Messaging)
- **APIs**: Google Maps, RESTful services
- **Configuration**: Environment-based configuration
- **Security**: API key management, secure credential handling

## 📊 **Project Statistics**

| Category | Count | Primary Technologies |
|----------|-------|---------------------|
| Mobile Apps | 9 | React Native, Expo, TypeScript |
| Desktop Apps | 1 | Angular, Electron, Node.js |
| Backend Services | 1 | Node.js, Express, MongoDB |
| **Total Projects** | **11** | **13+ Technologies** |

## 🏗️ **Architecture Highlights**

### **Mobile Development**
- **Cross-platform development** with React Native and Expo
- **State management** using Redux and Redux Toolkit
- **Navigation** with React Navigation
- **Real-time features** with WebSocket integration
- **Offline capabilities** with local storage

### **Desktop Development**
- **Hybrid desktop apps** using Electron and Angular
- **Real-time communication** with Socket.io
- **Material Design** implementation
- **Cross-platform compatibility**

### **Backend Development**
- **RESTful API design** with Express.js
- **Database modeling** with MongoDB and Mongoose
- **Authentication & authorization** with JWT
- **Security best practices** implementation

## 🔒 **Security & Best Practices**

- ✅ **Environment Variable Management** - All sensitive data externalized
- ✅ **API Security** - JWT authentication, secure endpoints
- ✅ **Database Security** - Parameterized queries, connection security
- ✅ **Code Quality** - ESLint, Prettier, TypeScript for type safety
- ✅ **Version Control** - Conventional commits, proper .gitignore
- ✅ **Configuration Management** - Environment-based configurations

## 📁 **Repository Structure**

```
evergreen-projects-portfolio/
├── 📱 Mobile Applications/
│   ├── FiberAlReactNative/          # Social media mobile app
│   ├── backsight-inventory/         # Inventory management
│   ├── backsight-logistic/          # Logistics management
│   ├── backsight-manager/           # Admin interface
│   ├── backsight-production/        # Production management
│   ├── backsight-property/          # Property management
│   ├── backsight-tasks/             # Task management
│   ├── backsightPro/                # Professional suite
│   └── orderApp/                    # Order management
├── 🖥️ Desktop Applications/
│   └── FiberAlDesktop/              # Angular/Electron desktop app
├── ⚙️ Backend Services/
│   └── orderAppBackend/             # Node.js API server
└── 📚 Documentation/
    ├── TECHNOLOGY_STACK.md          # Detailed tech documentation
    ├── PROJECT_ARCHITECTURE.md      # System architecture details
    └── SECURITY_PRACTICES.md        # Security implementation guide
```

## 🎯 **Professional Highlights**

- **Full-Stack Development**: Experience across frontend, backend, and mobile
- **Modern Technologies**: Proficiency in latest frameworks and tools
- **Enterprise Solutions**: Development of scalable business applications
- **Security-First Approach**: Implementation of security best practices
- **Code Quality**: Emphasis on maintainable, well-documented code
- **Cross-Platform Expertise**: Mobile, desktop, and web development

## 📞 **Contact Information**

**Email**: <EMAIL>  
**GitHub**: [github.com/amabd34](https://github.com/amabd34)  
**Portfolio**: [Evergreen Projects Portfolio](https://github.com/amabd34/evergreen-projects-portfolio)

---

**Built with ❤️ and professional expertise in modern software development**

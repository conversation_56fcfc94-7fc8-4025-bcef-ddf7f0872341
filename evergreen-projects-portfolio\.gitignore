# Evergreen Projects Portfolio - .gitignore
# This file ensures that no sensitive information is committed to the portfolio repository

# ================================
# CRITICAL SECURITY EXCLUSIONS
# ================================

# Environment Variables (NEVER commit actual .env files)
.env
.env.local
.env.development
.env.production
.env.staging
.env.test
*.env
!.env.example

# Firebase Configuration Files (CRITICAL: Keep out of version control)
GoogleService-Info.plist
google-services.json
firebase-config.json
firebase-adminsdk-*.json

# API Keys and Sensitive Files
*api-key*
*secret*
*token*
*credential*
secrets.json
credentials.json
config.env

# Certificates and Keys
*.key
*.pem
*.p12
*.pfx
*.keystore
*.jks
*.p8
*.mobileprovision

# ================================
# NODE.JS & NPM
# ================================

# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# ================================
# EXPO & REACT NATIVE
# ================================

# Expo
.expo/
dist/
web-build/
expo-env.d.ts

# Metro
.metro-health-check*

# React Native
*.orig.*

# ================================
# ANGULAR
# ================================

# Compiled output
/dist
/tmp
/out-tsc
/bazel-out

# Angular cache
/.angular/cache

# ================================
# MOBILE DEVELOPMENT
# ================================

# iOS
ios/Pods/
ios/build/
ios/DerivedData/
*.xcworkspace
*.xcuserdata

# Android
android/app/build/
android/build/
android/.gradle/
*.apk
*.aab

# ================================
# DATABASES
# ================================

# Database files
*.db
*.sqlite
*.sqlite3
*.db-journal

# ================================
# LOGS & TEMPORARY FILES
# ================================

# Logs
logs
*.log

# Temporary files
*.tmp
*.temp
.cache/

# ================================
# OPERATING SYSTEMS
# ================================

# macOS
.DS_Store
.AppleDouble
.LSOverride
Thumbs.db

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini

# Linux
*~

# ================================
# IDEs & EDITORS
# ================================

# Visual Studio Code
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
.history/*

# IntelliJ IDEA
.idea/
*.iml
*.ipr
*.iws

# Sublime Text
*.sublime-workspace
*.sublime-project

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc

# ================================
# BUILD TOOLS & PACKAGE MANAGERS
# ================================

# Webpack
.webpack/

# Parcel
.parcel-cache/

# TypeScript
*.tsbuildinfo

# ================================
# TESTING
# ================================

# Jest
coverage/
.nyc_output

# ================================
# PORTFOLIO SPECIFIC
# ================================

# Source code directories (replaced with documentation)
src/
app/
components/
controllers/
models/
routes/
services/
utils/

# Build artifacts
build/
dist/
out/

# Documentation that might contain sensitive info
SECURITY_AUDIT_REPORT.md

# ================================
# BACKUP FILES
# ================================

# Backup files
*.bak
*.backup
*.old
*.orig

# ================================
# MISCELLANEOUS
# ================================

# Thumbnails
._*

# Files that might appear in the root of a volume
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent

# Directories potentially created on remote AFP share
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk
